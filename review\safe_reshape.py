import math

def get_change_point_angle(axle, point2, collide_x):
    x0, y0 = axle
    x1, y1 = point2
    r = math.hypot(x1 - x0, y1 - y0)
    dx = collide_x - x0
    inside_sqrt = r**2 - dx**2

    if inside_sqrt < 0:
        raise ValueError(f"碰撞点 x={collide_x} 超出圆半径范围，sqrt({inside_sqrt}) 为负")

    dy = math.sqrt(inside_sqrt)
    y_upper = y0 + dy
    collide = [collide_x, y_upper]

    v1 = [x1 - x0, y1 - y0]
    v2 = [collide[0] - x0, collide[1] - y0]

    angle1 = math.atan2(v1[1], v1[0])
    angle2 = math.atan2(v2[1], v2[0])
    delta_angle = math.degrees(angle2 - angle1)
    return collide, delta_angle

def get_change_l1_point(axle, point, angle_deg):
    x0, y0 = axle
    x1, y1 = point
    angle_rad = math.radians(angle_deg)

    dx = x1 - x0
    dy = y1 - y0

    dx_new = dx * math.cos(angle_rad) - dy * math.sin(angle_rad)
    dy_new = dx * math.sin(angle_rad) + dy * math.cos(angle_rad)

    x2 = x0 + dx_new
    y2 = y0 + dy_new
    return [x2, y2]

# 初始设置
axle = [0, 0]
point1 = [483, 0]
point2 = [483, 434]
left_side = [521, 0]
right_side = [424, 0]
safe_left_side = [left_side[0] - (left_side[0] - right_side[0]) * 0.2, 0]
safe_right_side = [right_side[0] + (left_side[0] - right_side[0]) * 0.3, 0]

reshape_avoidance_angles = []
reshape_compensation_angles = []

i = 1
while True:
    print(f"第{i}次迭代:")

    # 1. 获取旋转角度并旋转 point1（绕 axle）
    collide_point, change_angle = get_change_point_angle(axle, point2, safe_left_side[0])
    print(f"完整旋转角度: {change_angle:.4f} 度")
    reshape_avoidance_angles.append(change_angle)
    change_point1 = get_change_l1_point(axle, point1, change_angle)
    change_point2 = get_change_l1_point(axle, point2, change_angle)
    print(f"旋转后 point1: {change_point1}")
    print(f"旋转后 point2: {change_point2}")

    # 2. 判断是否已经脱离绝缘子区域
    if collide_point[1] < 169.5:
        print("✅ 无需补偿：已出绝缘子区域")
        break

    # 3. 补偿（绕 change_point1 顺时针旋转）
    compensate = [safe_right_side[0],]
    collide_point2, compensate_angle = get_change_point_angle(change_point1, change_point2, compensate[0])
    print(f"补偿角度: {compensate_angle:.4f} 度")
    print(f"补偿后碰撞点: {collide_point2}")
    reshape_compensation_angles.append(compensate_angle)

    # 4. 应用补偿角度更新 point2（绕新的 point1 旋转）
    new_point2 = get_change_l1_point(change_point1, change_point2, compensate_angle)

    # 5. 更新 point1 和 point2，准备下一轮
    point1 = change_point1
    point2 = new_point2

    # 6. 最终判断是否还能碰撞 safe_left_side（绕 axle）：
    try:
        _, test_angle = get_change_point_angle(axle, point2, safe_left_side[0])
    except ValueError:
        print("✅ 当前 point2 无法再碰撞 safe_left_side，结束迭代")
        break

    print("-" * 50)
    i += 1

print("所有迭代的旋转角度:", reshape_avoidance_angles)
print("所有迭代的补偿角度:", reshape_compensation_angles)
