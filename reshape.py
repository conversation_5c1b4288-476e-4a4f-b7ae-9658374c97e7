import math

## 获取坐标和角度
def get_change_point_angle(axle, point2, collide_x):
    """
    已知圆心 axle、圆上一点 point2、碰撞点的 x 坐标 collide_x，
    计算碰撞点坐标和从 point2 逆时针旋转到该点的角度（度数）。
    返回：
    - 碰撞点坐标 [x, y]
    - 旋转角度（单位：度，范围 [0, 360)）
    """
    x0, y0 = axle
    x1, y1 = point2
    # 半径
    r = math.hypot(x1 - x0, y1 - y0)
    dx = collide_x - x0
    inside_sqrt = r**2 - dx**2

    if inside_sqrt < 0:
        raise ValueError(f"碰撞点 x={collide_x} 超出圆半径范围，sqrt({inside_sqrt}) 为负")

    dy = math.sqrt(inside_sqrt)
    y_upper = y0 + dy
    collide = [collide_x, y_upper]

    # 向量
    v1 = [x1 - x0, y1 - y0]
    v2 = [collide[0] - x0, collide[1] - y0]

    angle1 = math.atan2(v1[1], v1[0])  # 起点向量
    angle2 = math.atan2(v2[1], v2[0])  # 终点向量

    delta_angle = math.degrees(angle2 - angle1)

    # 归一化到 [-180, 180]
    while delta_angle > 180:
        delta_angle -= 360
    while delta_angle < -180:
        delta_angle += 360
    return collide, delta_angle

def get_change_l1_point(axle, point1, angle_deg):

    """
    计算 point1 绕 axle 逆时针旋转 angle_deg 度后的新位置。
    
    参数:
        axle: [x0, y0] 旋转中心
        point1: [x1, y1] 被旋转的点
        angle_deg: 旋转角度（单位：度，正值为逆时针）
    
    返回:
        point2: [x2, y2] 旋转后的点坐标
    """
    x0, y0 = axle
    x1, y1 = point1
    angle_rad = math.radians(angle_deg)

    # 将 point1 平移到以 axle 为原点的坐标系中
    dx = x1 - x0
    dy = y1 - y0

    # 旋转公式
    dx_new = dx * math.cos(angle_rad) - dy * math.sin(angle_rad)
    dy_new = dx * math.sin(angle_rad) + dy * math.cos(angle_rad)

    # 平移回原坐标系
    x2 = x0 + dx_new
    y2 = y0 + dy_new
    return [x2, y2]

axle = [0, 0]
point1 = [483, 0]
point2 = [483, 434]
collide_x = 521
reshape_avoidance_angles=[]
reshape_compensation_angles=[]


i = 1
while True:
    print(f"第{i}次迭代:")
    # 计算碰撞点和旋转角度
    collide_point, change_angle = get_change_point_angle(axle, point2, collide_x)
    print(f"碰撞点坐标: {collide_point}")
    print(f"旋转角度: {change_angle} 度")

    # 计算旋转后的 point1 坐标
    change_point1 = get_change_l1_point(axle, point1, change_angle)
    print(f"旋转后 point1 坐标: {change_point1}")

    # 检查是否需要补偿
    if collide_point[1] < 169.5:
        print("无需补偿: 已出绝缘子")
        break

    # 计算补偿点和角度
    compensate = [483, ]
    compensate, compensate_angle = get_change_point_angle(change_point1, collide_point, compensate[0])
    print(f"补偿点坐标: {compensate}")
    print(f"补偿角度: {compensate_angle} 度")
    
    print("-" * 50)
    i += 1  
    point1 = change_point1
    point2 = compensate
    reshape_avoidance_angles.append(change_angle)
    reshape_compensation_angles.append(compensate_angle)
print("所有迭代的旋转角度:", reshape_avoidance_angles)
print("所有迭代的补偿角度:", reshape_compensation_angles)
