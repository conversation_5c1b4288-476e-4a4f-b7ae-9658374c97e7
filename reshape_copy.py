import math

def get_change_point_angle(axle, point2, collide_x):
    x0, y0 = axle
    x1, y1 = point2
    r = math.hypot(x1 - x0, y1 - y0)
    dx = collide_x - x0
    inside_sqrt = r**2 - dx**2

    if inside_sqrt < 0:
        raise ValueError(f"碰撞点 x={collide_x} 超出圆半径范围，sqrt({inside_sqrt}) 为负")

    dy = math.sqrt(inside_sqrt)
    y_upper = y0 + dy
    collide = [collide_x, y_upper]

    v1 = [x1 - x0, y1 - y0]
    v2 = [collide[0] - x0, collide[1] - y0]

    angle1 = math.atan2(v1[1], v1[0])
    angle2 = math.atan2(v2[1], v2[0])
    delta_angle = math.degrees(angle2 - angle1)

    while delta_angle > 180:
        delta_angle -= 360
    while delta_angle < -180:
        delta_angle += 360
    return collide, delta_angle

def get_change_l1_point(axle, point, angle_deg):
    x0, y0 = axle
    x1, y1 = point
    angle_rad = math.radians(angle_deg)

    dx = x1 - x0
    dy = y1 - y0

    dx_new = dx * math.cos(angle_rad) - dy * math.sin(angle_rad)
    dy_new = dx * math.sin(angle_rad) + dy * math.cos(angle_rad)

    x2 = x0 + dx_new
    y2 = y0 + dy_new
    return [x2, y2]

# 初始设置
axle = [0, 0]
point1 = [483, 0]
point2 = [483, 434]
collide_x = 521  # 初始碰撞线 x 值
reshape_avoidance_angles=[]
reshape_compensation_angles=[]

i = 1
while True:
    print(f"第{i}次迭代:")
    
    # 1. 获取原始角度
    collide_point, change_angle = get_change_point_angle(axle, point2, collide_x)
    print(f"完整旋转角度: {change_angle:.4f} 度")

    # 2. 只使用 50% 的角度（更保守）
    real_angle = change_angle * 0.5
    print(f"实际应用旋转角度（50%）: {real_angle:.4f} 度")

    # 3. 用 real_angle 计算旋转后点位
    new_point1 = get_change_l1_point(axle, point1, real_angle)
    new_point2 = get_change_l1_point(axle, point2, real_angle)
    print(f"旋转后 point1: {new_point1}")
    print(f"旋转后 point2: {new_point2}")

    # 4. 判断是否出绝缘子区域
    if new_point2[1] < 169.5:
        print("✅ 无需补偿：已出绝缘子区域")
        break

    # 5. 补偿阶段（使 new_point2 碰撞 x=483）
    compensate_x = 483
    collide_point2, compensate_angle = get_change_point_angle(new_point1, new_point2, compensate_x)
    print(f"补偿角度: {compensate_angle:.4f} 度")
    print(f"补偿后碰撞点: {collide_point2}")

    # 6. 累加旋转角度，更新下一轮使用的点
    point1 = get_change_l1_point(axle, point1, real_angle + compensate_angle)
    point2 = collide_point2
    print("-" * 50)
    i += 1
    reshape_avoidance_angles.append(change_angle)
    reshape_compensation_angles.append(compensate_angle)
print("所有迭代的旋转角度:", reshape_avoidance_angles)
print("所有迭代的补偿角度:", reshape_compensation_angles)
