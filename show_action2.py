import numpy as np
import matplotlib.pyplot as plt
import math

# 鼠标悬停显示坐标的函数
def on_mouse_move(event, ax, coord_text):
    if event.inaxes == ax:
        x, y = event.xdata, event.ydata
        if x is not None and y is not None:
            coord_text.set_text(f'坐标: ({x:.3f}, {y:.3f})')
            coord_text.set_position((x, y))
            coord_text.set_visible(True)
        else:
            coord_text.set_visible(False)
        plt.draw()
    else:
        coord_text.set_visible(False)
        plt.draw()

# 检查线段是否与垂直线段相交
def check_line_intersection(p1, p2, vertical_x, vertical_y1, vertical_y2):
    """
    检查线段p1-p2是否与垂直线段相交
    vertical_x: 垂直线段的x坐标
    vertical_y1, vertical_y2: 垂直线段的y坐标范围
    """
    x1, y1 = p1
    x2, y2 = p2

    # 如果线段完全在垂直线的一侧，则不相交
    if (x1 <= vertical_x and x2 <= vertical_x) or (x1 >= vertical_x and x2 >= vertical_x):
        return False, None

    # 计算交点的y坐标
    if x2 == x1:  # 避免除零
        return False, None

    # 线性插值计算交点y坐标
    t = (vertical_x - x1) / (x2 - x1)
    intersection_y = y1 + t * (y2 - y1)

    # 检查交点是否在垂直线段范围内
    if min(vertical_y1, vertical_y2) <= intersection_y <= max(vertical_y1, vertical_y2):
        return True, (vertical_x, intersection_y)

    return False, None

# 从reshape.py计算得出的精确避免碰撞角度
def get_change_point_angle(axle, point2, collide_x):
    """
    已知圆心 axle、圆上一点 point2、碰撞点的 x 坐标 collide_x，
    计算碰撞点坐标和从 point2 逆时针旋转到该点的角度（度数）。
    """
    x0, y0 = axle
    x1, y1 = point2
    # 半径
    r = math.hypot(x1 - x0, y1 - y0)
    dx = collide_x - x0
    inside_sqrt = r**2 - dx**2

    if inside_sqrt < 0:
        return None, 0.0  # 无法到达碰撞点

    dy = math.sqrt(inside_sqrt)
    y_upper = y0 + dy
    collide = [collide_x, y_upper]

    # 向量
    v1 = [x1 - x0, y1 - y0]
    v2 = [collide[0] - x0, collide[1] - y0]

    angle1 = math.atan2(v1[1], v1[0])  # 起点向量
    angle2 = math.atan2(v2[1], v2[0])  # 终点向量

    delta_angle = math.degrees(angle2 - angle1)

    # 归一化到 [-180, 180]
    while delta_angle > 180:
        delta_angle -= 360
    while delta_angle < -180:
        delta_angle += 360
    return collide, delta_angle

# 改进的碰撞避免系统
def calculate_safe_distance(point, obstacle_x, obstacle_y1, obstacle_y2):
    """计算点到障碍物的安全距离"""
    px, py = point

    # 如果点的y坐标在障碍物范围内，距离就是x坐标差
    if obstacle_y1 <= py <= obstacle_y2:
        return abs(px - obstacle_x)

    # 否则计算到障碍物端点的距离
    dist1 = math.sqrt((px - obstacle_x)**2 + (py - obstacle_y1)**2)
    dist2 = math.sqrt((px - obstacle_x)**2 + (py - obstacle_y2)**2)
    return min(dist1, dist2)

def predict_collision_risk(C, D, obstacle_lines, steps_ahead=5):
    """预测未来几步的碰撞风险"""
    risk_level = 0
    min_distance = float('inf')

    for obs_x, obs_y1, obs_y2 in obstacle_lines:
        # 检查当前碰撞
        intersects, _ = check_line_intersection(C, D, obs_x, obs_y1, obs_y2)
        if intersects:
            risk_level = max(risk_level, 10)  # 最高风险

        # 计算距离
        d_distance = calculate_safe_distance(D, obs_x, obs_y1, obs_y2)
        min_distance = min(min_distance, d_distance)

        # 根据距离评估风险
        if d_distance < 0.2:
            risk_level = max(risk_level, 8)
        elif d_distance < 0.5:
            risk_level = max(risk_level, 6)
        elif d_distance < 1.0:
            risk_level = max(risk_level, 4)
        elif d_distance < 1.5:
            risk_level = max(risk_level, 2)

    return risk_level, min_distance

def apply_adaptive_safety_factor(original_angle, risk_level, iteration):
    """根据风险等级自适应调整安全系数"""
    base_factors = {
        0: 1.0,    # 无风险，使用原角度
        2: 0.8,    # 低风险，80%
        4: 0.6,    # 中低风险，60%
        6: 0.4,    # 中高风险，40%
        8: 0.3,    # 高风险，30%
        10: 0.2    # 极高风险，20%
    }

    # 随着迭代次数增加，变得更保守，但不要过于激进
    iteration_factor = max(0.5, 1.0 - iteration * 0.05)  # 改为每次减少5%，最低50%

    safety_factor = base_factors.get(risk_level, 0.5) * iteration_factor
    safe_angle = original_angle * safety_factor

    return safe_angle, safety_factor

# 从reshape_copy.py计算得出的角度（这些是60%的角度）
reshape_avoidance_angles = [5.296471856152966, 5.690827119170685, 6.19150481045735, 6.858221026487896, 7.813136602128935, 9.366695358035313]
reshape_compensation_angles = [-3.0798281720806475, -3.2871894256370977, -3.5434835750012494, -3.8721709187772566, -4.317274068838789, -4.9779570989395845]

def rotate_point(origin, point, angle_deg):
    angle_rad = np.radians(angle_deg)
    ox, oy = origin
    px, py = point
    qx = ox + np.cos(angle_rad) * (px - ox) - np.sin(angle_rad) * (py - oy)
    qy = oy + np.sin(angle_rad) * (px - ox) + np.cos(angle_rad) * (py - oy)
    return qx, qy

# 参数
AC = 4.83
AB = 4.34
EF_len = 3.84
d1 = 0.59
d2 = 0.54

# 原始点坐标
A0 = (0, 0)
B = (0, AB)
C0 = (-AC, 0)
D0 = (-AC, AB)
E = (D0[0] + d1, D0[1] + EF_len / 2)
F = (D0[0] + d1, D0[1] - EF_len / 2)
E1 = (D0[0] - d2, D0[1] + EF_len / 2)
F1 = (D0[0] - d2, D0[1] - EF_len / 2)

# 初始化当前点位（用于累积旋转）
C_current = C0
D_current = D0

# 计算初始CD线段长度
initial_CD_length = np.sqrt((D0[0] - C0[0])**2 + (D0[1] - C0[1])**2)

# 定义障碍物线段 (x坐标, y1, y2)
obstacle_lines = [
    (E[0], F[1], E[1]),   # EF线段
    (E1[0], F1[1], E1[1]) # E1F1线段
]

# 使用reshape.py计算出的精确旋转参数
angles_ac_cd = reshape_avoidance_angles
compensation_angles = reshape_compensation_angles

# 动画帧数
steps = 60

# 创建一个窗口显示所有迭代
fig, ax = plt.subplots(figsize=(12, 10))
ax.set_aspect('equal')
ax.grid(True)
ax.set_xlim(-10, 10)
ax.set_ylim(-10, 10)
ax.axhline(0, color='gray', linewidth=0.5)
ax.axvline(0, color='gray', linewidth=0.5)

# 添加坐标显示文本
coord_text = ax.text(0, 0, '', fontsize=10, bbox=dict(boxstyle="round,pad=0.3",
                    facecolor="yellow", alpha=0.7), visible=False)

# 连接鼠标移动事件
fig.canvas.mpl_connect('motion_notify_event',
                      lambda event: on_mouse_move(event, ax, coord_text))

# 绘制静态结构
ax.plot([A0[0], B[0]], [A0[1], B[1]], 'r-', linewidth=2, label='AB')
ax.plot([E[0], F[0]], [E[1], F[1]], 'gray', linestyle='--', linewidth=2, label='EF')
ax.plot([E1[0], F1[0]], [E1[1], F1[1]], 'gray', linestyle='--', linewidth=2, label="E₁F₁")

# 标注点位
for pt, name in zip([A0, B, C0, D0, E, F, E1, F1], ['A', 'B', 'C', 'D', 'E', 'F', 'E₁', 'F₁']):
    ax.text(pt[0], pt[1] + 0.2, name, color='blue', fontsize=12, fontweight='bold')

# 创建动画线段
line_ac, = ax.plot([], [], 'o-', color='blue', linewidth=3, markersize=6, label='AC')
line_cd, = ax.plot([], [], 'o-', color='green', linewidth=3, markersize=6, label='CD')

# 添加迭代信息显示
iteration_text = ax.text(-9, 8, '', fontsize=14, bbox=dict(boxstyle="round,pad=0.5",
                        facecolor="lightblue", alpha=0.8))

ax.legend(loc='upper right', fontsize=12)

# 改进的自适应迭代执行
max_iterations = 25  # 增加最大迭代次数
iteration = 0

while iteration < max_iterations:
    # 检查是否已经安全（降低安全脱离的要求）
    current_risk, current_min_dist = predict_collision_risk(C_current, D_current, obstacle_lines)
    if current_risk <= 2 and current_min_dist > 1.5:  # 降低要求：风险≤2且距离>1.5
        print(f"✅ 已安全脱离！经过{iteration}次迭代")
        ax.set_title("安全脱离所有障碍物！", fontsize=16, fontweight='bold', color='green')
        iteration_text.set_text(f"安全脱离！\n经过{iteration}次迭代\n最小距离: {current_min_dist:.3f}")
        plt.pause(2.0)
        break

    # 获取角度（如果超出预定义角度，使用最后一个角度）
    if iteration < len(reshape_avoidance_angles):
        original_angle = reshape_avoidance_angles[iteration]
        original_comp = reshape_compensation_angles[iteration]
    else:
        # 使用最后一个角度继续
        original_angle = reshape_avoidance_angles[-1]
        original_comp = reshape_compensation_angles[-1]
        print(f"使用最后一个角度继续第{iteration+1}次迭代")
    # 获取原始角度
    original_angle = reshape_avoidance_angles[iteration]
    original_comp = reshape_compensation_angles[iteration]

    # 评估当前风险等级
    risk_level, min_distance = predict_collision_risk(C_current, D_current, obstacle_lines)

    # 应用自适应安全系数
    safe_angle, safety_factor = apply_adaptive_safety_factor(original_angle, risk_level, iteration)
    safe_comp, comp_safety_factor = apply_adaptive_safety_factor(abs(original_comp), risk_level, iteration)
    safe_comp = -safe_comp if original_comp < 0 else safe_comp  # 保持原始符号

    step_angle = safe_angle / steps
    step_comp = safe_comp / steps

    # 每次迭代都从当前 C, D 开始
    C = C_current
    D = D_current

    # 更新迭代信息
    iteration_text.set_text(f"第 {iteration + 1} 次迭代\n"
                           f"原始角度: {original_angle:.2f}°\n"
                           f"安全角度: {safe_angle:.2f}° ({safety_factor:.1%})\n"
                           f"风险等级: {risk_level}/10\n"
                           f"最小距离: {min_distance:.3f}")

    # 分两个阶段：先旋转到碰撞点，再补偿避开
    collision_step = int(steps * 0.7)  # 70%的步骤用于旋转到碰撞点
    compensation_step = steps - collision_step  # 30%的步骤用于补偿

    # 第一阶段：旋转到碰撞点
    rotation_step_angle = safe_angle / collision_step

    for step in range(collision_step):
        # AC绕A旋转
        C = rotate_point(A0, C, rotation_step_angle)
        # D也跟着AC一起旋转（保持CD的相对位置）
        D = rotate_point(A0, D, rotation_step_angle)

        # 检查是否与E'F'碰撞
        intersects_e1f1, intersection_point = check_line_intersection(C, D, E1[0], F1[1], E1[1])

        # 实时评估风险并更新颜色
        current_risk, current_min_dist = predict_collision_risk(C, D, obstacle_lines)

        # 根据风险等级设置颜色
        if current_risk >= 8:
            line_color = 'red'      # 高风险
            line_width = 4
        elif current_risk >= 6:
            line_color = 'orange'   # 中高风险
            line_width = 3
        elif current_risk >= 4:
            line_color = 'yellow'   # 中风险
            line_width = 3
        elif current_risk >= 2:
            line_color = 'lightblue' # 低风险
            line_width = 2
        else:
            line_color = 'green'    # 安全
            line_width = 2

        line_cd.set_color(line_color)
        line_cd.set_linewidth(line_width)

        # 更新线段
        line_ac.set_data([A0[0], C[0]], [A0[1], C[1]])
        line_cd.set_data([C[0], D[0]], [C[1], D[1]])

        # 更新标题显示当前状态
        risk_text = f"风险:{current_risk}/10" if current_risk > 0 else "安全"
        distance_text = f"距离:{current_min_dist:.3f}" if current_min_dist != float('inf') else ""

        ax.set_title(f"第 {iteration + 1}/{max_iterations} 次迭代 - 旋转阶段 ({step+1}/{collision_step}) - {risk_text} {distance_text}",
                    fontsize=12, fontweight='bold', color=line_color)

        plt.draw()
        plt.pause(0.03)

    # 第二阶段：补偿旋转避开碰撞
    if compensation_step > 0:
        compensation_step_angle = safe_comp / compensation_step

        for step in range(compensation_step):
            # CD绕新的C点做补偿旋转
            D = rotate_point(C, D, compensation_step_angle)

            # 检查碰撞状态
            intersects_e1f1, _ = check_line_intersection(C, D, E1[0], F1[1], E1[1])

            # 更新线段颜色
            if intersects_e1f1:
                line_cd.set_color('orange')  # 补偿过程中仍有碰撞显示橙色
                line_cd.set_linewidth(4)
            else:
                line_cd.set_color('blue')   # 成功避开碰撞显示蓝色
                line_cd.set_linewidth(3)

            # 更新线段
            line_ac.set_data([A0[0], C[0]], [A0[1], C[1]])
            line_cd.set_data([C[0], D[0]], [C[1], D[1]])

            # 更新标题显示补偿阶段
            ax.set_title(f"第 {iteration + 1}/{max_iterations} 次迭代 - 补偿阶段 (步骤 {step+1}/{compensation_step})" +
                        (f" - 仍有碰撞" if intersects_e1f1 else " - 成功避开"),
                        fontsize=14, fontweight='bold',
                        color='orange' if intersects_e1f1 else 'blue')

            plt.draw()
            plt.pause(0.03)

    # 更新C_current和D_current为旋转后的新位置
    C_current = C
    D_current = D

    # 在每次迭代结束后稍作停顿
    plt.pause(0.5)

    # 增加迭代计数器
    iteration += 1

# 最终标题
ax.set_title("所有旋转完成！", fontsize=16, fontweight='bold', color='green')
iteration_text.set_text("动画完成\n所有5次迭代已执行")
plt.show()
