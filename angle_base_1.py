import math

# 计算碰撞点坐标
def get_collide_point(l1, l2, side):
    y = round(math.sqrt(math.pow(l1, 2)+math.pow(l2, 2)-math.pow(side, 2)), 2)
    x = side
    return x, y
# 计算到达碰撞点需要的角度
def get_collide_angle(xiecollide_yian, collide_x, collide_y,point_x, point_y):
    di_side = round(math.sqrt(math.pow((point_x-collide_x), 2) + math.pow((point_y-collide_y), 2)), 2)
    change_angle = round(math.degrees(math.asin((di_side/2)/xiecollide_yian)), 2)
    return change_angle
# 计算碰撞点后的l1末端坐标
def get_change_l1_point(l1, collide_angle):
    change_l1_point_x = round(l1*math.cos(math.radians(collide_angle)), 2)
    change_l1_point_y = round(l1*math.sin(math.radians(collide_angle)), 2)
    return change_l1_point_x, change_l1_point_y
 #计算碰撞点后的l2末端坐标
def get_change_l2_point(l1, collide_x, collide_y, change_l1_point_x, change_l1_point_y):
    change_l2_point_x = l1
    change_l2_point_middle_y1 = round(math.sqrt(math.pow((collide_x-change_l1_point_x),2)+math.pow((collide_y-change_l1_point_y),2)-math.pow((l1-change_l1_point_x), 2))+change_l1_point_y, 2)
    change_l2_point_middle_y2 = round(-math.sqrt(math.pow((collide_x-change_l1_point_x),2)+math.pow((collide_y-change_l1_point_y),2)-math.pow((l1-change_l1_point_x), 2))+change_l1_point_y, 2)
    if (change_l2_point_middle_y1 > change_l2_point_middle_y2 or change_l2_point_middle_y1 == change_l2_point_middle_y2):
        change_l2_point_y = change_l2_point_middle_y1
    else:
        change_l2_point_y = change_l2_point_middle_y2
    return change_l2_point_x, change_l2_point_y

#获取补偿角度
def get_compensate_angle(l2, collide_x, collide_y,point_x, point_y):
    di_side = round(math.sqrt(math.pow((point_x-collide_x), 2) + math.pow((point_y-collide_y), 2)), 2)
    compensate_angle = 2*math.degrees(math.asin((di_side/2)/l2))
    return -compensate_angle

l1= -483
l2 = 434
xiecollide_yian = round(math.sqrt(math.pow(l1, 2) + math.pow(l2, 2)), 2)
side = -521
i = 1
while True:
    print(f"第{i}次迭代:")
# 计算碰撞点坐标
    collide_x, collide_y = get_collide_point(l1, l2, side)
    print(f"碰撞点坐标: ({collide_x}, {collide_y})")
# 计算到达碰撞点需要的角度
    change_angle = get_collide_angle(xiecollide_yian, l1, l2, collide_x, collide_y)
    print(f"到达碰撞点需要的角度: {change_angle}°")
# 计算碰撞点后的l1末端坐标和l2末端坐标
    change_l1_point_x, change_l1_point_y = get_change_l1_point(l1, change_angle)
    print(f"碰撞点后的l1末端坐标: ({change_l1_point_x}, {change_l1_point_y})")
    change_l2_point_x, change_l2_point_y = get_change_l2_point(l1, collide_x, collide_y, change_l1_point_x, change_l1_point_y)
    print(f"碰撞点后的l2末端坐标: ({change_l2_point_x}, {change_l2_point_y})")
# 获取补偿角度
    if(change_l2_point_y<169.5):
        print(f"无需补偿:已出绝缘子")
        break
    compensate_angle = get_compensate_angle(l2, collide_x, collide_y, change_l2_point_x, change_l2_point_y)
    print(f"补偿角度: {compensate_angle}°")
    print("-" * 50)
    i = i + 1
    l2 = change_l2_point_y
    xiecollide_yian = math.sqrt(math.pow(l1, 2) + math.pow(l2, 2))






