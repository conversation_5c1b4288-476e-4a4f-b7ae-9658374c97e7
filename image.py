import numpy as np
import matplotlib.pyplot as plt

# 坐标旋转函数：point 绕 origin 逆时针旋转 angle（度）
def rotate_point(origin, point, angle_deg):
    angle_rad = np.radians(angle_deg)
    ox, oy = origin
    px, py = point
    qx = ox + np.cos(angle_rad) * (px - ox) - np.sin(angle_rad) * (py - oy)
    qy = oy + np.sin(angle_rad) * (px - ox) + np.cos(angle_rad) * (py - oy)
    return qx, qy

# 参数
AC = 4.83
AB = 4.34
EF_len = 3.84
d1 = 0.59
d2 = 0.54

# 原始点坐标
A0 = (0, 0)
B = (0, AB)
C0 = (-AC, 0)
D0 = (-AC, AB)
E = (D0[0] + d1, D0[1] + EF_len / 2)
F = (D0[0] + d1, D0[1] - EF_len / 2)
E1 = (D0[0] - d2, D0[1] + EF_len / 2)
F1 = (D0[0] - d2, D0[1] - EF_len / 2)

# 旋转参数
angles_ac_cd = [-5.296471856152966, -5.99005331842257, -7.049827663117262, -8.98090946800982, -15.278875393837179]
compensation_angles = [5.024220245337556, 5.029046752028973, 5.043777885352931, 5.0907355096416955, 0]

# 创建图形窗口
fig, ax = plt.subplots(figsize=(10, 10))
ax.set_aspect('equal')
ax.grid(True)
ax.set_xlim(-10, 10)
ax.set_ylim(-10, 10)

# 绘制静态结构
def draw_segment(p1, p2, label=None, color='black'):
    line, = ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'o-', color=color)
    if label:
        mx, my = (p1[0] + p2[0]) / 2, (p1[1] + p2[1]) / 2
        ax.text(mx, my, label, fontsize=10, color=color)
    return line

draw_segment(A0, B, 'AB', 'red')
draw_segment(E, F, 'EF', 'gray')
draw_segment(E1, F1, 'E₁F₁', 'gray')
for pt, name in zip([A0, B, C0, D0, E, F, E1, F1], ['A', 'B', 'C', 'D', 'E', 'F', 'E₁', 'F₁']):
    ax.text(pt[0], pt[1] + 0.2, name, color='blue', fontsize=10)
ax.axhline(0, color='gray', linewidth=0.5)
ax.axvline(0, color='gray', linewidth=0.5)

# 动画步骤
steps = 60  # 总步数
line_ac, = ax.plot([], [], 'o-', color='blue', linewidth=2, label='AC')
line_cd, = ax.plot([], [], 'o-', color='green', linewidth=2, label='CD')

# 初始旋转角度
current_angle_ac_cd = 0  # 当前AC和CD的旋转角度
current_comp_angle = 0  # 当前补偿角度

for i in range(steps):  # 总步数
    # 获取当前的增量角度
    angle_ac_cd = angles_ac_cd[i % len(angles_ac_cd)]  # 获取当前旋转角度
    comp_angle = compensation_angles[i % len(compensation_angles)]  # 获取补偿角度

    # 更新当前旋转角度
    current_angle_ac_cd += angle_ac_cd
    current_comp_angle += comp_angle

    # 步骤1：绕 A 旋转 AC 和 CD
    A = A0  # 保持 A 不动
    C = rotate_point(A, C0, current_angle_ac_cd)
    D = rotate_point(A, D0, current_angle_ac_cd)

    # 步骤2：计算补偿角度
    C_new = rotate_point(C, C, current_comp_angle)  # C 自身不动
    D_new = rotate_point(C, D, current_comp_angle)

    # 更新线段
    line_ac.set_data([A[0], C_new[0]], [A[1], C_new[1]])
    line_cd.set_data([C_new[0], D_new[0]], [C_new[1], D_new[1]])

    fig.canvas.draw_idle()
    plt.pause(0.05)

plt.title("线段 AC, CD 绕 A 和 C 联合旋转动画")
plt.legend()
plt.show()
