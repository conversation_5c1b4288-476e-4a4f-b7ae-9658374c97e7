import numpy as np
import matplotlib.pyplot as plt
import math

# 鼠标悬停显示坐标的函数
def on_mouse_move(event, ax, coord_text):
    if event.inaxes == ax:
        x, y = event.xdata, event.ydata
        if x is not None and y is not None:
            coord_text.set_text(f'坐标: ({x:.3f}, {y:.3f})')
            coord_text.set_position((x, y))
            coord_text.set_visible(True)
        else:
            coord_text.set_visible(False)
        plt.draw()
    else:
        coord_text.set_visible(False)
        plt.draw()

# 检查线段是否与垂直线段相交
def check_line_intersection(p1, p2, vertical_x, vertical_y1, vertical_y2):
    """
    检查线段p1-p2是否与垂直线段相交
    vertical_x: 垂直线段的x坐标
    vertical_y1, vertical_y2: 垂直线段的y坐标范围
    """
    x1, y1 = p1
    x2, y2 = p2

    # 如果线段完全在垂直线的一侧，则不相交
    if (x1 <= vertical_x and x2 <= vertical_x) or (x1 >= vertical_x and x2 >= vertical_x):
        return False, None

    # 计算交点的y坐标
    if x2 == x1:  # 避免除零
        return False, None

    # 线性插值计算交点y坐标
    t = (vertical_x - x1) / (x2 - x1)
    intersection_y = y1 + t * (y2 - y1)

    # 检查交点是否在垂直线段范围内
    if min(vertical_y1, vertical_y2) <= intersection_y <= max(vertical_y1, vertical_y2):
        return True, (vertical_x, intersection_y)

    return False, None

# 从reshape.py计算得出的精确避免碰撞角度
def get_change_point_angle(axle, point2, collide_x):
    """
    已知圆心 axle、圆上一点 point2、碰撞点的 x 坐标 collide_x，
    计算碰撞点坐标和从 point2 逆时针旋转到该点的角度（度数）。
    """
    x0, y0 = axle
    x1, y1 = point2
    # 半径
    r = math.hypot(x1 - x0, y1 - y0)
    dx = collide_x - x0
    inside_sqrt = r**2 - dx**2

    if inside_sqrt < 0:
        return None, 0.0  # 无法到达碰撞点

    dy = math.sqrt(inside_sqrt)
    y_upper = y0 + dy
    collide = [collide_x, y_upper]

    # 向量
    v1 = [x1 - x0, y1 - y0]
    v2 = [collide[0] - x0, collide[1] - y0]

    angle1 = math.atan2(v1[1], v1[0])  # 起点向量
    angle2 = math.atan2(v2[1], v2[0])  # 终点向量

    delta_angle = math.degrees(angle2 - angle1)

    # 归一化到 [-180, 180]
    while delta_angle > 180:
        delta_angle -= 360
    while delta_angle < -180:
        delta_angle += 360
    return collide, delta_angle

# 简化版本：只显示避障过程，不做额外操作

# 从safe_reshape.py计算得出的新角度（直接使用，不做额外修改）
reshape_avoidance_angles = [2.5179059361765646, 7.275469942200232, 8.820380135122742, 12.072259936811445]
reshape_compensation_angles = [-6.406664679230618, -6.4062386929040205, -6.4127860468905045, -6.472838147214523]

def rotate_point(origin, point, angle_deg):
    angle_rad = np.radians(angle_deg)
    ox, oy = origin
    px, py = point
    qx = ox + np.cos(angle_rad) * (px - ox) - np.sin(angle_rad) * (py - oy)
    qy = oy + np.sin(angle_rad) * (px - ox) + np.cos(angle_rad) * (py - oy)
    return qx, qy

# 参数
AC = 4.83
AB = 4.34
EF_len = 3.84
d1 = 0.59
d2 = 0.54

# 原始点坐标
A0 = (0, 0)
B = (0, AB)
C0 = (-AC, 0)
D0 = (-AC, AB)
E = (D0[0] + d1, D0[1] + EF_len / 2)
F = (D0[0] + d1, D0[1] - EF_len / 2)
E1 = (D0[0] - d2, D0[1] + EF_len / 2)
F1 = (D0[0] - d2, D0[1] - EF_len / 2)

# 初始化当前点位（用于累积旋转）
C_current = C0
D_current = D0

# 计算初始CD线段长度
initial_CD_length = np.sqrt((D0[0] - C0[0])**2 + (D0[1] - C0[1])**2)

# 定义障碍物线段 (x坐标, y1, y2)
obstacle_lines = [
    (E[0], F[1], E[1]),   # EF线段
    (E1[0], F1[1], E1[1]) # E1F1线段
]

# 使用reshape.py计算出的精确旋转参数
angles_ac_cd = reshape_avoidance_angles
compensation_angles = reshape_compensation_angles

# 动画帧数
steps = 60

# 创建一个窗口显示所有迭代
fig, ax = plt.subplots(figsize=(12, 10))
ax.set_aspect('equal')
ax.grid(True)
ax.set_xlim(-10, 10)
ax.set_ylim(-10, 10)
ax.axhline(0, color='gray', linewidth=0.5)
ax.axvline(0, color='gray', linewidth=0.5)

# 添加坐标显示文本
coord_text = ax.text(0, 0, '', fontsize=10, bbox=dict(boxstyle="round,pad=0.3",
                    facecolor="yellow", alpha=0.7), visible=False)

# 连接鼠标移动事件
fig.canvas.mpl_connect('motion_notify_event',
                      lambda event: on_mouse_move(event, ax, coord_text))

# 绘制静态结构
ax.plot([A0[0], B[0]], [A0[1], B[1]], 'r-', linewidth=2, label='AB')
ax.plot([E[0], F[0]], [E[1], F[1]], 'gray', linestyle='--', linewidth=2, label='EF')
ax.plot([E1[0], F1[0]], [E1[1], F1[1]], 'gray', linestyle='--', linewidth=2, label="E₁F₁")

# 标注点位
for pt, name in zip([A0, B, C0, D0, E, F, E1, F1], ['A', 'B', 'C', 'D', 'E', 'F', 'E₁', 'F₁']):
    ax.text(pt[0], pt[1] + 0.2, name, color='blue', fontsize=12, fontweight='bold')

# 创建动画线段
line_ac, = ax.plot([], [], 'o-', color='blue', linewidth=3, markersize=6, label='AC')
line_cd, = ax.plot([], [], 'o-', color='green', linewidth=3, markersize=6, label='CD')

# 添加迭代信息显示
iteration_text = ax.text(-9, 8, '', fontsize=14, bbox=dict(boxstyle="round,pad=0.5",
                        facecolor="lightblue", alpha=0.8))

ax.legend(loc='upper right', fontsize=12)

# 直接使用safe_reshape.py的角度进行演示
for iteration in range(len(reshape_avoidance_angles)):
    # 直接使用safe_reshape.py计算的角度，不做任何修改
    angle_ac_cd_total = reshape_avoidance_angles[iteration]
    comp_angle_total = reshape_compensation_angles[iteration]

    step_angle = angle_ac_cd_total / steps
    step_comp = comp_angle_total / steps

    # 每次迭代都从当前 C, D 开始
    C = C_current
    D = D_current

    # 更新迭代信息
    iteration_text.set_text(f"第 {iteration + 1} 次迭代\n"
                           f"旋转角度: {angle_ac_cd_total:.3f}°\n"
                           f"补偿角度: {comp_angle_total:.3f}°\n"
                           f"safe_reshape.py计算结果")

    # 简化的动画：直接显示旋转和补偿过程
    for step in range(steps):
        # 第一步：AC绕A旋转 step_angle
        C = rotate_point(A0, C, step_angle)

        # 第二步：D也跟着AC一起旋转（保持CD的相对位置）
        D = rotate_point(A0, D, step_angle)

        # 第三步：CD绕新的C点做补偿旋转 step_comp
        if step_comp != 0:
            D = rotate_point(C, D, step_comp)

        # 检查是否与障碍物相交（仅用于显示颜色）
        intersects_ef, _ = check_line_intersection(C, D, E[0], F[1], E[1])
        intersects_e1f1, _ = check_line_intersection(C, D, E1[0], F1[1], E1[1])

        # 根据碰撞状态设置颜色
        if intersects_ef or intersects_e1f1:
            line_cd.set_color('red')    # 碰撞时显示红色
            line_cd.set_linewidth(4)
        else:
            line_cd.set_color('green')  # 安全时显示绿色
            line_cd.set_linewidth(3)

        # 更新线段
        line_ac.set_data([A0[0], C[0]], [A0[1], C[1]])
        line_cd.set_data([C[0], D[0]], [C[1], D[1]])

        # 更新标题
        ax.set_title(f"第 {iteration + 1}/{len(reshape_avoidance_angles)} 次迭代 - 步骤 {step+1}/{steps}",
                    fontsize=14, fontweight='bold')

        plt.draw()
        plt.pause(0.02)

    # 更新C_current和D_current为旋转后的新位置
    C_current = C
    D_current = D

    # 在每次迭代结束后稍作停顿
    plt.pause(0.5)

    # 增加迭代计数器
    iteration += 1

# 最终标题
ax.set_title("所有旋转完成！", fontsize=16, fontweight='bold', color='green')
iteration_text.set_text("动画完成\n所有5次迭代已执行")
plt.show()
