import numpy as np
import math

# 参数
AC = 4.83
AB = 4.34
EF_len = 3.84
d1 = 0.59
d2 = 0.54

# 坐标
A0 = (0, 0)
C0 = (-AC, 0)
D0 = (-AC, AB)
E = (D0[0] + d1, D0[1] + EF_len / 2)
F = (D0[0] + d1, D0[1] - EF_len / 2)
E1 = (D0[0] - d2, D0[1] + EF_len / 2)
F1 = (D0[0] - d2, D0[1] - EF_len / 2)

def check_line_intersection(p1, p2, vertical_x, vertical_y1, vertical_y2):
    x1, y1 = p1
    x2, y2 = p2
    
    if (x1 <= vertical_x and x2 <= vertical_x) or (x1 >= vertical_x and x2 >= vertical_x):
        return False, None
    
    if x2 == x1:
        return False, None
    
    t = (vertical_x - x1) / (x2 - x1)
    intersection_y = y1 + t * (y2 - y1)
    
    if min(vertical_y1, vertical_y2) <= intersection_y <= max(vertical_y1, vertical_y2):
        return True, (vertical_x, intersection_y)
    
    return False, None

def rotate_point(origin, point, angle_deg):
    angle_rad = math.radians(angle_deg)
    ox, oy = origin
    px, py = point
    qx = ox + math.cos(angle_rad) * (px - ox) - math.sin(angle_rad) * (py - oy)
    qy = oy + math.sin(angle_rad) * (px - ox) + math.cos(angle_rad) * (py - oy)
    return qx, qy

def get_change_point_angle(axle, point2, collide_x):
    x0, y0 = axle
    x1, y1 = point2
    r = math.hypot(x1 - x0, y1 - y0)
    dx = collide_x - x0
    inside_sqrt = r**2 - dx**2

    if inside_sqrt < 0:
        return None, 0.0

    dy = math.sqrt(inside_sqrt)
    y_upper = y0 + dy
    collide = [collide_x, y_upper]

    v1 = [x1 - x0, y1 - y0]
    v2 = [collide[0] - x0, collide[1] - y0]

    angle1 = math.atan2(v1[1], v1[0])
    angle2 = math.atan2(v2[1], v2[0])

    delta_angle = math.degrees(angle2 - angle1)

    while delta_angle > 180:
        delta_angle -= 360
    while delta_angle < -180:
        delta_angle += 360
    return collide, delta_angle

# reshape_copy.py计算出的角度（已经是60%的角度）
reshape_avoidance_angles = [5.296471856152966, 5.690827119170685, 6.19150481045735, 6.858221026487896, 7.813136602128935, 9.366695358035313]
reshape_compensation_angles = [-3.0798281720806475, -3.2871894256370977, -3.5434835750012494, -3.8721709187772566, -4.317274068838789, -4.9779570989395845]

print("分析reshape_copy.py计算的角度是否足够安全:")
print(f"E'F'线段位置: x={E1[0]:.3f}, y从{F1[1]:.3f}到{E1[1]:.3f}")

C_current = C0
D_current = D0

for iteration in range(len(reshape_avoidance_angles)):
    angle_total = reshape_avoidance_angles[iteration]
    comp_total = reshape_compensation_angles[iteration]
    
    print(f"\n=== 第{iteration+1}次迭代 ===")
    print(f"旋转角度: {angle_total:.3f}° (已经是60%)")
    print(f"补偿角度: {comp_total:.3f}°")
    print(f"开始位置: C({C_current[0]:.3f}, {C_current[1]:.3f}), D({D_current[0]:.3f}, {D_current[1]:.3f})")
    
    # 计算完整的100%角度（反推）
    full_angle = angle_total / 0.6
    print(f"对应的100%角度: {full_angle:.3f}°")
    
    # 模拟旋转过程
    steps = 60
    collision_step = int(steps * 0.7)
    rotation_step_angle = angle_total / collision_step
    
    C = C_current
    D = D_current
    collision_detected = False
    collision_steps = []
    
    # 第一阶段：旋转
    for step in range(collision_step):
        C = rotate_point(A0, C, rotation_step_angle)
        D = rotate_point(A0, D, rotation_step_angle)
        
        # 检查与E'F'的碰撞
        intersects, _ = check_line_intersection(C, D, E1[0], F1[1], E1[1])
        if intersects:
            collision_detected = True
            collision_steps.append(step + 1)
    
    if collision_detected:
        print(f"⚠️  旋转阶段检测到{len(collision_steps)}次碰撞")
        print(f"   碰撞步骤: {collision_steps[:3]}..." if len(collision_steps) > 3 else f"   碰撞步骤: {collision_steps}")
    else:
        print("✅ 旋转阶段无碰撞")
    
    # 第二阶段：补偿
    compensation_step = steps - collision_step
    if compensation_step > 0:
        compensation_step_angle = comp_total / compensation_step
        compensation_collisions = 0
        
        for step in range(compensation_step):
            D = rotate_point(C, D, compensation_step_angle)
            intersects, _ = check_line_intersection(C, D, E1[0], F1[1], E1[1])
            if intersects:
                compensation_collisions += 1
        
        if compensation_collisions > 0:
            print(f"⚠️  补偿阶段仍有{compensation_collisions}次碰撞")
        else:
            print("✅ 补偿阶段成功避开碰撞")
    
    print(f"最终位置: C({C[0]:.3f}, {C[1]:.3f}), D({D[0]:.3f}, {D[1]:.3f})")
    
    # 计算与E'F'的最小距离
    distance_to_ef = abs(D[0] - E1[0])
    print(f"距离E'F': {distance_to_ef:.3f}")
    
    C_current = C
    D_current = D

print(f"\n=== 分析结论 ===")
print("问题可能的原因:")
print("1. reshape_copy.py使用60%角度，但可能还不够保守")
print("2. 补偿角度的方向或大小可能需要调整")
print("3. 动画的视觉效果可能让人感觉更接近")
print("4. 需要考虑增加更大的安全边距")
